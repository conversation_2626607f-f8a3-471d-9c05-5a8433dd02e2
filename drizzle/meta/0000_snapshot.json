{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public.challenges": {"name": "challenges", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "question": {"name": "question", "type": "text", "primaryKey": false, "notNull": true}, "lesson_id": {"name": "lesson_id", "type": "integer", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"challenges_lesson_id_lessons_id_fk": {"name": "challenges_lesson_id_lessons_id_fk", "tableFrom": "challenges", "tableTo": "lessons", "schemaTo": "public", "columnsFrom": ["lesson_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.challenge_options": {"name": "challenge_options", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "challenge_id": {"name": "challenge_id", "type": "integer", "primaryKey": false, "notNull": true}, "option": {"name": "option", "type": "text", "primaryKey": false, "notNull": true}, "correct": {"name": "correct", "type": "boolean", "primaryKey": false, "notNull": true}, "image_src": {"name": "image_src", "type": "text", "primaryKey": false, "notNull": false}, "audio_src": {"name": "audio_src", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"challenge_options_challenge_id_challenges_id_fk": {"name": "challenge_options_challenge_id_challenges_id_fk", "tableFrom": "challenge_options", "tableTo": "challenges", "schemaTo": "public", "columnsFrom": ["challenge_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.challenge_progress": {"name": "challenge_progress", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "challenge_id": {"name": "challenge_id", "type": "integer", "primaryKey": false, "notNull": true}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {}, "foreignKeys": {"challenge_progress_challenge_id_challenges_id_fk": {"name": "challenge_progress_challenge_id_challenges_id_fk", "tableFrom": "challenge_progress", "tableTo": "challenges", "schemaTo": "public", "columnsFrom": ["challenge_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_subscription": {"name": "user_subscription", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_price_id": {"name": "stripe_price_id", "type": "text", "primaryKey": false, "notNull": true}, "stripe_current_period_end": {"name": "stripe_current_period_end", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_subscription_user_id_key": {"columns": ["user_id"], "nullsNotDistinct": false, "name": "user_subscription_user_id_key"}, "user_subscription_stripe_customer_id_key": {"columns": ["stripe_customer_id"], "nullsNotDistinct": false, "name": "user_subscription_stripe_customer_id_key"}, "user_subscription_stripe_subscription_id_key": {"columns": ["stripe_subscription_id"], "nullsNotDistinct": false, "name": "user_subscription_stripe_subscription_id_key"}}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.courses": {"name": "courses", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "alt_code": {"name": "alt_code", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.user_progress": {"name": "user_progress", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "text", "primaryKey": true, "notNull": true}, "user_name": {"name": "user_name", "type": "text", "primaryKey": false, "notNull": true, "default": "'User'"}, "user_img_src": {"name": "user_img_src", "type": "text", "primaryKey": false, "notNull": true, "default": "'/logo.svg'"}, "active_course_id": {"name": "active_course_id", "type": "integer", "primaryKey": false, "notNull": false}, "hearts": {"name": "hearts", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "points": {"name": "points", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "gems": {"name": "gems", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {"user_progress_active_course_id_courses_id_fk": {"name": "user_progress_active_course_id_courses_id_fk", "tableFrom": "user_progress", "tableTo": "courses", "schemaTo": "public", "columnsFrom": ["active_course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.units": {"name": "units", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"units_course_id_courses_id_fk": {"name": "units_course_id_courses_id_fk", "tableFrom": "units", "tableTo": "courses", "schemaTo": "public", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.lessons": {"name": "lessons", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "unit_id": {"name": "unit_id", "type": "integer", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"lessons_unit_id_units_id_fk": {"name": "lessons_unit_id_units_id_fk", "tableFrom": "lessons", "tableTo": "units", "schemaTo": "public", "columnsFrom": ["unit_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.type": {"name": "type", "values": ["SELECT", "HINT"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}