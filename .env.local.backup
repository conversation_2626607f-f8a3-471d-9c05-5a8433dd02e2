# Miriani Well - AI-Powered Wellness Platform (Duolingo-Inspired)
# Production Environment Configuration

# Database Configuration
DATABASE_URL=file:./local.db

# Clerk Authentication (<PERSON>niWell-DL-main)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Y2FsbS1tb25rZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_PuxkxnYOGaTqoucmIe73HyAdRzpU0BmhKu3LxKsOM5

# Clerk URLs
# Frontend API URL: https://calm-monkfish-47.clerk.accounts.dev
# Backend API URL: https://api.clerk.com
# JWKS URL: https://calm-monkfish-47.clerk.accounts.dev/.well-known/jwks.json

# Google Gemini AI (for Luna companion)
GOOGLE_GEMINI_API_KEY=your_google_gemini_api_key

# Stripe Payment Processing
STRIPE_API_KEY=your_stripe_api_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Admin Users (Dr. Uzo Nwankpa and others)
CLERK_ADMIN_IDS=user_id_1,user_id_2

# Optional: Additional Configuration
# NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
# NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
