@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body,
:root {
  @apply h-full;
  overflow-x: hidden;
  position: relative;
}

/* Hide scrollbars globally */
::-webkit-scrollbar {
  display: none;
}

html {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

body {
  -ms-overflow-style: none;
}

/* Floating animation keyframes */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes float-slow {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes float-medium {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Prevent horizontal scroll on all elements */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Ensure containers don't exceed viewport width */
  .container,
  section,
  main,
  header,
  footer {
    max-width: 100vw;
    overflow-x: hidden;
  }
}

/* Custom 3D shadow effects for pricing cards */
@layer utilities {
  .shadow-3xl {
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .shadow-4xl {
    box-shadow:
      0 35px 60px -12px rgba(0, 0, 0, 0.3),
      0 10px 25px -5px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  }

  .card-3d {
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  .card-3d-hover:hover {
    transform: rotateY(5deg) rotateX(5deg) translateZ(20px);
  }

  /* Pricing Cards Layout */
  .pricing-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .pricing-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  }

  .pricing-card.popular {
    border-color: #22c55e;
    position: relative;
  }

  .pricing-card.popular::before {
    content: "⭐ Most Popular";
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #22c55e;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
  }

  .pricing-header {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .pricing-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }

  .pricing-price {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 0.25rem;
  }

  .pricing-period {
    color: #6b7280;
    font-size: 0.875rem;
  }

  .pricing-description {
    color: #6b7280;
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
  }

  .pricing-features {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
    flex-grow: 1;
  }

  .pricing-features li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    color: #4b5563;
    font-size: 0.875rem;
  }

  .pricing-features li::before {
    content: "✓";
    color: #22c55e;
    font-weight: bold;
    font-size: 1rem;
  }

  .pricing-button {
    width: 100%;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .pricing-button.primary {
    background: #22c55e;
    color: white;
  }

  .pricing-button.primary:hover {
    background: #16a34a;
  }

  .pricing-button.secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .pricing-button.secondary:hover {
    background: #e5e7eb;
  }

  .pricing-button.tertiary {
    background: #3b82f6;
    color: white;
  }

  .pricing-button.tertiary:hover {
    background: #2563eb;
  }

  .pricing-guarantee {
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.75rem;
    color: #6b7280;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .pricing-container {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 1rem;
    }

    .pricing-card {
      padding: 1.25rem;
      min-height: 350px;
    }

    .pricing-price {
      font-size: 2rem;
    }
  }
}

/* Lesson button dynamic positioning */
.lesson-button-position {
  right: var(--right-position, 0px);
}
