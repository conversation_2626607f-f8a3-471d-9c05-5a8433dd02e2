"use client";

import { useState, useTransition, useCallback, useMemo } from "react";

import Image from "next/image";
import { useRouter } from "next/navigation";
import Confetti from "react-confetti";
import { useAudio, useWindowSize, useMount } from "react-use";

import { MAX_HEARTS } from "@/constants";
import { challengeOptions, challenges, userSubscription } from "@/db/schema";
import { useLanguage } from "@/contexts/LanguageContext";
import { useHeartsModal } from "@/store/use-hearts-modal";
import { usePracticeModal } from "@/store/use-practice-modal";

import { Challenge } from "./challenge";
import { Footer } from "./footer";
import { Header } from "./header";
import { QuestionBubble } from "./question-bubble";
import { ResultCard } from "./result-card";

type QuizProps = {
  initialPercentage: number;
  initialHearts: number;
  initialLessonId: number;
  initialLessonChallenges: (typeof challenges.$inferSelect & {
    completed: boolean;
    challengeOptions: (typeof challengeOptions.$inferSelect)[];
  })[];
  userSubscription:
  | (typeof userSubscription.$inferSelect & {
    isActive: boolean;
  })
  | null;
};

export const Quiz = ({
  initialPercentage,
  initialHearts,
  initialLessonId,
  initialLessonChallenges,
  userSubscription,
}: QuizProps) => {
  const { t, currentLanguage, translateQuizContent } = useLanguage();

  // Define types for quiz content
  type QuizItem = {
    text?: string;
    question?: string;
    translationKey?: string;
  };

  // Helper function to translate quiz content using translation keys if available
  const getTranslatedContent = (item: QuizItem): string => {
    if (item?.translationKey) {
      const translation = t(item.translationKey);
      if (translation !== item.translationKey) {
        return translation;
      }
    }
    // Use the general translation function as fallback
    return translateQuizContent(item?.text || item?.question || '');
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [correctAudio, _c, correctControls] = useAudio({ src: "/correct.wav" });
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [incorrectAudio, _i, incorrectControls] = useAudio({
    src: "/incorrect.wav",
  });
  const [finishAudio] = useAudio({
    src: "/finish.mp3",
    autoPlay: true,
  });
  const { width, height } = useWindowSize();

  const router = useRouter();
  const [pending] = useTransition();
  const { open: openHeartsModal } = useHeartsModal();
  const { open: openPracticeModal } = usePracticeModal();

  useMount(() => {
    if (initialPercentage === 100) openPracticeModal();
  });

  const [lessonId] = useState(initialLessonId);
  const [hearts, setHearts] = useState(initialHearts);
  const [percentage, setPercentage] = useState(() => {
    return initialPercentage === 100 ? 0 : initialPercentage;
  });
  const [challenges] = useState(initialLessonChallenges);
  const [activeIndex, setActiveIndex] = useState(() => {
    const uncompletedIndex = challenges.findIndex(
      (challenge) => !challenge.completed
    );

    return uncompletedIndex === -1 ? 0 : uncompletedIndex;
  });

  const [selectedOption, setSelectedOption] = useState<number>();
  const [status, setStatus] = useState<"none" | "wrong" | "correct">("none");

  const challenge = challenges[activeIndex];
  const options = useMemo(() => challenge?.challengeOptions ?? [], [challenge]);

  const onNext = useCallback(() => {
    setActiveIndex((current) => current + 1);
  }, []);

  const onSelect = useCallback((id: number) => {
    if (status !== "none") return;

    setSelectedOption(id);
  }, [status]);

  const onFinish = useCallback(() => {
    router.push("/learn");
  }, [router]);

  const onContinue = useCallback(() => {
    if (!selectedOption) return;

    if (status === "wrong") {
      setStatus("none");
      setSelectedOption(undefined);
      return;
    }

    if (status === "correct") {
      onNext();
      setStatus("none");
      setSelectedOption(undefined);
      return;
    }

    const correctOption = options.find((option) => option.correct);

    if (!correctOption) return;

    if (correctOption.id === selectedOption) {
      // Correct answer - use mock progression instead of API call
      void correctControls.play();
      setStatus("correct");
      setPercentage((prev) => prev + 100 / challenges.length);

      // This is a practice
      if (initialPercentage === 100) {
        setHearts((prev) => Math.min(prev + 1, MAX_HEARTS));
      }
    } else {
      // Wrong answer - use mock progression instead of API call
      void incorrectControls.play();
      setStatus("wrong");
      setHearts((prev) => Math.max(prev - 1, 0));

      // Check if hearts are 0 and user is not subscribed
      if (hearts <= 1 && !userSubscription?.isActive) {
        openHeartsModal();
        return;
      }
    }
  }, [
    selectedOption,
    status,
    options,
    correctControls,
    setStatus,
    setPercentage,
    challenges.length,
    initialPercentage,
    setHearts,
    incorrectControls,
    hearts,
    userSubscription?.isActive,
    openHeartsModal,
    onNext,
  ]);

  if (!challenge) {
    return (
      <>
        {finishAudio}
        <Confetti
          recycle={false}
          numberOfPieces={500}
          tweenDuration={10_000}
          width={width}
          height={height}
        />
        <div className="mx-auto flex h-full max-w-lg flex-col items-center justify-center gap-y-4 text-center lg:gap-y-8">
          <Image
            src="/finish.svg"
            alt="Finish"
            className="hidden lg:block"
            height={100}
            width={100}
          />

          <Image
            src="/finish.svg"
            alt="Finish"
            className="block lg:hidden"
            height={100}
            width={100}
          />

          <h1 className="text-lg font-bold text-neutral-700 lg:text-3xl">
            {t('quiz.complete')} <br /> {t('quiz.congratulations')}
          </h1>

          <div className="flex w-full items-center gap-x-4">
            <ResultCard variant="points" value={challenges.length * 10} />
            <ResultCard
              variant="hearts"
              value={userSubscription?.isActive ? Infinity : hearts}
            />
          </div>
        </div>

        <Footer
          lessonId={lessonId}
          status="completed"
          onCheck={onFinish}
        />
      </>
    );
  }

  const title =
    challenge.type === "ASSIST"
      ? t("quiz.selectCorrectMeaning")
      : getTranslatedContent(challenge);

  return (
    <div className="flex flex-col min-h-screen">
      {incorrectAudio}
      {correctAudio}
      {finishAudio}
      
      {/* Header with hearts, progress bar and language toggle at top */}
      <Header
        hearts={hearts}
        percentage={percentage}
        hasActiveSubscription={!!userSubscription?.isActive}
      />

      {/* Main content container */}
      <div className="flex-1 py-4 w-full max-w-6xl mx-auto px-4">
        {/* Question title */}
        <h1 className="text-center text-xl font-bold text-neutral-700 mb-6 lg:text-2xl">
          {title}
        </h1>
        
        {/* Question bubble if needed */}
        {challenge.type === "ASSIST" && (
          <div className="w-full max-w-3xl mx-auto mb-6">
            <QuestionBubble question={getTranslatedContent(challenge)} />
          </div>
        )}
        
        {/* Main quiz container - centered with cards in middle */}
        <div className="flex flex-col items-center justify-center">
          {/* Cards container - centered in page */}
          <div className="w-full max-w-3xl mx-auto">
            <Challenge
              options={options}
              onSelect={onSelect}
              status={status}
              selectedOption={selectedOption}
              disabled={pending}
              type={challenge.type}
              getTranslatedContent={getTranslatedContent}
            />
          </div>
        </div>
      </div>
      
      {/* Fixed check button at bottom right */}
      <div className="fixed bottom-8 right-8 z-10">
        <button
          onClick={onContinue}
          disabled={pending || !selectedOption}
          className={`px-8 py-3 rounded-lg font-semibold text-white shadow-md transform transition-all duration-200 
            ${status === "wrong" ? "bg-rose-500 hover:bg-rose-600" : "bg-green-500 hover:bg-green-600"} 
            ${!selectedOption && "opacity-50 cursor-not-allowed"}`}
        >
          {status === "none" && t('quiz.check')}
          {status === "correct" && t('quiz.next')}
          {status === "wrong" && t('quiz.skip')}
        </button>
      </div>

      {/* Status display for correct/wrong answers (hidden when not needed) */}
      <div className={`fixed bottom-0 left-0 right-0 transition-opacity duration-300 ${status !== "none" ? "opacity-100" : "opacity-0 pointer-events-none"}`}>
        {status === "correct" && (
          <div className="bg-green-100 py-4 px-6 flex items-center justify-center">
            <div className="flex items-center text-base font-bold text-green-500 lg:text-xl">
              <CheckCircle className="mr-4 h-6 w-6" />
              {t('quiz.correctAnswer')}
            </div>
          </div>
        )}

        {status === "wrong" && (
          <div className="bg-rose-100 py-4 px-6 flex items-center justify-center">
            <div className="flex items-center text-base font-bold text-rose-500 lg:text-xl">
              <XCircle className="mr-4 h-6 w-6" />
              {t('quiz.wrongAnswer')}
            </div>
          </div>
        )}
      </div>
      
      {/* Hidden original footer */}
      <div className="hidden">
        <Footer
          disabled={pending || !selectedOption}
          status={status}
          onCheck={onContinue}
        />
      </div>
    </div>
  );
};
